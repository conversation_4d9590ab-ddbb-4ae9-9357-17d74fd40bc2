'use client';

import React, { useState, useEffect } from 'react';
import { Search, Brain, Trash2, Plus, Loader2 } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { toast } from 'sonner';
import { getUserMemories, searchMemories, addMemory, deleteMemory, Memory } from '@/lib/api';
import styles from './MemoryPanel.module.scss';

export const MemoryPanel = () => {
  const [memories, setMemories] = useState<Memory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [newMemoryContent, setNewMemoryContent] = useState('');
  const [isAddingMemory, setIsAddingMemory] = useState(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  // Load all memories on component mount
  useEffect(() => {
    loadMemories();
  }, []);

  const loadMemories = async () => {
    setIsLoading(true);
    try {
      const response = await getUserMemories(100);
      setMemories(response.memories);
    } catch (error) {
      console.error('Failed to load memories:', error);
      toast.error('Failed to load memories');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) {
      loadMemories();
      return;
    }

    setIsSearching(true);
    try {
      const response = await searchMemories(searchQuery, 50, 0.3);
      setMemories(response.memories);
    } catch (error) {
      console.error('Failed to search memories:', error);
      toast.error('Failed to search memories');
    } finally {
      setIsSearching(false);
    }
  };

  const handleAddMemory = async () => {
    if (!newMemoryContent.trim()) {
      toast.error('Please enter memory content');
      return;
    }

    setIsAddingMemory(true);
    try {
      await addMemory(newMemoryContent);
      toast.success('Memory added successfully');
      setNewMemoryContent('');
      setIsDialogOpen(false);
      loadMemories(); // Refresh the list
    } catch (error) {
      console.error('Failed to add memory:', error);
      toast.error('Failed to add memory');
    } finally {
      setIsAddingMemory(false);
    }
  };

  const handleDeleteMemory = async (memoryId: string) => {
    try {
      await deleteMemory(memoryId);
      toast.success('Memory deleted successfully');
      loadMemories(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete memory:', error);
      toast.error('Failed to delete memory');
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  return (
    <div className={styles.root}>
      <div className={styles.header}>
        <div className={styles.searchContainer}>
          <div className={styles.searchInputWrapper}>
            <Search className={styles.searchIcon} size={16} />
            <Input
              placeholder="Search memories..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              className={styles.searchInput}
            />
          </div>
          <Button
            onClick={handleSearch}
            disabled={isSearching}
            size="sm"
            className={styles.searchButton}
          >
            {isSearching ? <Loader2 className="animate-spin" size={16} /> : 'Search'}
          </Button>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm" className={styles.addButton}>
              <Plus size={16} className="mr-1" />
              Add Memory
            </Button>
          </DialogTrigger>
          <DialogContent className={styles.dialogContent}>
            <DialogHeader>
              <DialogTitle>Add New Memory</DialogTitle>
            </DialogHeader>
            <div className={styles.dialogBody}>
              <Textarea
                placeholder="Enter memory content..."
                value={newMemoryContent}
                onChange={(e) => setNewMemoryContent(e.target.value)}
                className={styles.memoryTextarea}
                rows={4}
              />
              <div className={styles.dialogActions}>
                <Button
                  variant="outline"
                  onClick={() => setIsDialogOpen(false)}
                  disabled={isAddingMemory}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleAddMemory}
                  disabled={isAddingMemory || !newMemoryContent.trim()}
                >
                  {isAddingMemory ? (
                    <>
                      <Loader2 className="animate-spin mr-2" size={16} />
                      Adding...
                    </>
                  ) : (
                    'Add Memory'
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      <ScrollArea className={styles.memoriesContainer}>
        {isLoading ? (
          <div className={styles.loadingContainer}>
            <Loader2 className="animate-spin" size={24} />
            <span>Loading memories...</span>
          </div>
        ) : memories.length === 0 ? (
          <div className={styles.emptyState}>
            <Brain size={48} className={styles.emptyIcon} />
            <h3>No memories found</h3>
            <p>
              {searchQuery
                ? 'Try adjusting your search query'
                : 'Start by adding your first memory'}
            </p>
          </div>
        ) : (
          <div className={styles.memoriesList}>
            {memories.map((memory, index) => (
              <Card key={memory.id || index} className={styles.memoryCard}>
                <CardHeader className={styles.memoryCardHeader}>
                  <div className={styles.memoryCardTitleRow}>
                    <CardTitle className={styles.memoryCardTitle}>
                      Memory {index + 1}
                    </CardTitle>
                    <div className={styles.memoryActions}>
                      {memory.score && (
                        <Badge variant="secondary" className={styles.scoreBadge}>
                          {(memory.score * 100).toFixed(0)}%
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteMemory(memory.id)}
                        className={styles.deleteButton}
                      >
                        <Trash2 size={14} />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className={styles.memoryCardContent}>
                  <p className={styles.memoryContent}>{memory.content}</p>
                  {memory.metadata && Object.keys(memory.metadata).length > 0 && (
                    <div className={styles.metadata}>
                      <span className={styles.metadataLabel}>Metadata:</span>
                      <pre className={styles.metadataContent}>
                        {JSON.stringify(memory.metadata, null, 2)}
                      </pre>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
