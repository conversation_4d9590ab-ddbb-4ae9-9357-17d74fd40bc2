@use '@styles/index' as *;

.root {
  @extend %flex-column;
  height: 100%;
  gap: 16px;
  padding: 16px;
}

.header {
  @extend %flex-column;
  gap: 12px;
}

.searchContainer {
  @extend %flex-column;
  gap: 8px;
}

.searchInputWrapper {
  position: relative;
  @extend %flex-row;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 12px;
  color: var(--muted-foreground);
  z-index: 1;
}

.searchInput {
  padding-left: 36px !important;
}

.searchButton {
  align-self: flex-start;
}

.addButton {
  align-self: flex-start;
}

.memoriesContainer {
  flex: 1;
  height: 100%;
}

.loadingContainer {
  @extend %flex-column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px 20px;
  color: var(--muted-foreground);
}

.emptyState {
  @extend %flex-column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 40px 20px;
  text-align: center;
  color: var(--muted-foreground);

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--foreground);
  }

  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.4;
  }
}

.emptyIcon {
  opacity: 0.5;
}

.memoriesList {
  @extend %flex-column;
  gap: 12px;
  padding-bottom: 16px;
}

.memoryCard {
  border: 1px solid var(--border);
  border-radius: 8px;
  background: var(--card);
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--border-hover, var(--border));
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.memoryCardHeader {
  padding: 12px 16px 8px 16px;
}

.memoryCardTitleRow {
  @extend %flex-between;
  align-items: flex-start;
  gap: 12px;
}

.memoryCardTitle {
  font-size: 14px;
  font-weight: 600;
  margin: 0;
  color: var(--foreground);
}

.memoryActions {
  @extend %flex-row;
  align-items: center;
  gap: 8px;
}

.scoreBadge {
  font-size: 11px;
  padding: 2px 6px;
  background: var(--secondary);
  color: var(--secondary-foreground);
}

.deleteButton {
  padding: 4px;
  height: auto;
  width: auto;
  color: var(--muted-foreground);
  
  &:hover {
    color: var(--destructive);
    background: var(--destructive-hover, rgba(239, 68, 68, 0.1));
  }
}

.memoryCardContent {
  padding: 0 16px 16px 16px;
}

.memoryContent {
  margin: 0;
  font-size: 13px;
  line-height: 1.5;
  color: var(--foreground);
  word-wrap: break-word;
  white-space: pre-wrap;
}

.metadata {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--border);
}

.metadataLabel {
  font-size: 11px;
  font-weight: 600;
  color: var(--muted-foreground);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metadataContent {
  margin: 4px 0 0 0;
  font-size: 11px;
  color: var(--muted-foreground);
  background: var(--muted);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre;
}

.dialogContent {
  max-width: 500px;
}

.dialogBody {
  @extend %flex-column;
  gap: 16px;
  margin-top: 16px;
}

.memoryTextarea {
  min-height: 100px;
  resize: vertical;
}

.dialogActions {
  @extend %flex-row;
  justify-content: flex-end;
  gap: 8px;
}

// Responsive adjustments
@media screen and (max-width: $nj-breakpoint-tablet) {
  .root {
    padding: 12px;
  }

  .searchContainer {
    gap: 6px;
  }

  .memoriesList {
    gap: 8px;
  }

  .memoryCard {
    border-radius: 6px;
  }

  .memoryCardHeader {
    padding: 10px 12px 6px 12px;
  }

  .memoryCardContent {
    padding: 0 12px 12px 12px;
  }

  .memoryContent {
    font-size: 12px;
  }
}
