'use client';

import { Fragment } from 'react';
import { ChatCircleDots, Icon, Brain } from '@phosphor-icons/react';
import classNames from 'classnames';
import styles from './NavSidebar.module.scss';
import { NinjaLogo } from '@/components/ui/NinjaLogo';
import { SVG_SIZE_M, SVG_SIZE_XL } from '@/constants';
import Link from 'next/link';
import * as React from 'react';
import { Button } from '@/components/ui/Button/index';
import { useSidebar } from '@/components/ui/sidebar';
import { LeftPanelSections } from '@/types';
import { AddConversationButton } from '@/components/ui/AddConversationButton';
import { useAccounts } from '@/hooks/use-accounts';
import { UserImage } from '@/components/sidebar/SidebarLeft/components/UserImage';

interface ActionItem {
  section: LeftPanelSections;
  Icon: Icon;
  tooltipContent: string;
  visible: boolean;
  withBorder?: boolean;
  withRedDot?: boolean;
}

export const NavSidebar = () => {
  const {
    isLeftSidePanelOpen,
    setIsLeftSidePanelOpen,
    leftPanelSection,
    setLeftPanelData,
  } = useSidebar();

  const ACTION_LIST: ActionItem[] = [
    {
      section: LeftPanelSections.THREAD_LIST,
      Icon: ChatCircleDots,
      tooltipContent: 'Super Ninja history',
      visible: true,
      withRedDot: false,
    },
    {
      section: LeftPanelSections.MEMORY,
      Icon: Brain,
      tooltipContent: 'Memories',
      visible: true,
      withRedDot: false,
    },
  ];

  const handleButtonClick = (panelType: LeftPanelSections) => {
    if (leftPanelSection === panelType && isLeftSidePanelOpen) {
      setIsLeftSidePanelOpen(false);
      return;
    }

    setLeftPanelData({
      panelType,
      isExpanded: true,
    });
  };

  return (
    <div
      className={classNames(styles.root, {
        [styles.withBorder]: isLeftSidePanelOpen,
        [styles.withShadow]: !isLeftSidePanelOpen,
      })}
    >
      <div className={styles.container}>
        <Link href="/dashboard">
          <NinjaLogo size={SVG_SIZE_XL} />
        </Link>

        <AddConversationButton appearance="gradient-plus-button" />

        {ACTION_LIST.map(
          ({
            section,
            Icon,
            tooltipContent,
            visible,
            withBorder,
            withRedDot,
          }) => (
            <Fragment key={section}>
              {visible && (
                <Button
                  color={
                    isLeftSidePanelOpen && leftPanelSection === section
                      ? 'active-transparent'
                      : 'transparent'
                  }
                  shape="round"
                  onClick={() => handleButtonClick(section)}
                  tooltipContent={tooltipContent}
                  className={classNames({
                    [styles.withRedDot]: withRedDot,
                  })}
                >
                  <Icon size={SVG_SIZE_M} />
                </Button>
              )}

              {withBorder && <hr className="divider" />}
            </Fragment>
          ),
        )}
      </div>

      <div className={styles.container}>
        <Button
          color="transparent"
          shape="round"
          onClick={() => handleButtonClick(LeftPanelSections.USER_MENU)}
          tooltipContent="Account"
        >
          <UserImage />
        </Button>
      </div>
    </div>
  );
};
